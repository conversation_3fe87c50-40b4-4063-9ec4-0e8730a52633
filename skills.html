<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta content="width=device-width, initial-scale=1.0" name="viewport">
  <title>Skills - <PERSON><PERSON></title>
  <meta name="description" content="Technical skills and expertise of <PERSON><PERSON> - <PERSON>-<PERSON><PERSON>, Data Scientist, and AI Engineer. Programming languages, frameworks, tools, and ML/DL libraries.">
  <meta name="keywords" content="Programming Skills, Python, JavaScript, React, Machine Learning, Data Science, AI, Technical Skills, Karan Rajendiran">

  <!-- Favicons -->
  <link href="" rel="icon">
  <link href="assets/img/apple-touch-icon.png" rel="apple-touch-icon">

  <!-- Fonts -->
  <link href="https://fonts.googleapis.com" rel="preconnect">
  <link href="https://fonts.gstatic.com" rel="preconnect" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Raleway:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">

  <!-- Vendor CSS Files -->
  <link href="assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
  <link href="assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
  <link href="assets/vendor/aos/aos.css" rel="stylesheet">
  <link href="assets/vendor/swiper/swiper-bundle.min.css" rel="stylesheet">
  <link href="assets/vendor/glightbox/css/glightbox.min.css" rel="stylesheet">

  <!-- Main CSS File -->
  <link href="assets/css/main.css" rel="stylesheet">
</head>

<body class="skills-page">

  <header id="header" class="header d-flex align-items-center fixed-top">
    <div class="container-fluid container-xl position-relative d-flex align-items-center justify-content-between">

      <a href="index.html" class="logo d-flex align-items-center">
        <h1 class="sitename">Karan Rajendiran <img src="assets/img/profile-img.jpg" class="img-fluid" alt=""></h1>
      </a>

      <nav id="navmenu" class="navmenu">
        <ul>
          <li><a href="index.html">Home</a></li>
          <li><a href="about.html">About</a></li>
          <li class="dropdown"><a href="#"><span>Projects</span> <i class="bi bi-chevron-down toggle-dropdown"></i></a>
            <ul>
              <li><a href="portfolio.html">All Projects</a></li>
              <li><a href="portfolio.html#software-development">Software Development</a></li>
              <li><a href="portfolio.html#data-science">Data Science</a></li>
              <li><a href="portfolio.html#ai-ml">AI/ML Engineering</a></li>
            </ul>
          </li>
          <li><a href="skills.html" class="active">Skills</a></li>
          <li><a href="blog.html">Blog</a></li>
          <li><a href="contact.html">Contact</a></li>
        </ul>
        <i class="mobile-nav-toggle d-xl-none bi bi-list"></i>
      </nav>

    </div>
  </header>

  <main class="main">

    <!-- Page Title -->
    <div class="page-title" data-aos="fade">
      <div class="container">
        <nav class="breadcrumbs">
          <ol>
            <li><a href="index.html">Home</a></li>
            <li class="current">Skills</li>
          </ol>
        </nav>
        <h1>Technical Skills & Expertise</h1>
        <p>A comprehensive overview of my technical skills, tools, and technologies I work with as a Full-Stack Developer, Data Scientist, and AI Engineer.</p>
      </div>
    </div><!-- End Page Title -->

    <!-- Skills Section -->
    <section id="skills" class="skills section">
      <div class="container">

        <!-- Languages & Frameworks -->
        <div class="row gy-4">
          <div class="col-lg-12" data-aos="fade-up" data-aos-delay="100">
            <h3 class="skills-category-title">
              <i class="bi bi-code-slash"></i>
              Programming Languages & Frameworks
            </h3>
            <div class="skills-grid">
              <div class="skill-item">
                <div class="skill-icon">
                  <i class="bi bi-filetype-py"></i>
                </div>
                <div class="skill-info">
                  <h4>Python</h4>
                  <div class="progress">
                    <div class="progress-bar" style="width: 95%">95%</div>
                  </div>
                </div>
              </div>

              <div class="skill-item">
                <div class="skill-icon">
                  <i class="bi bi-filetype-js"></i>
                </div>
                <div class="skill-info">
                  <h4>JavaScript</h4>
                  <div class="progress">
                    <div class="progress-bar" style="width: 90%">90%</div>
                  </div>
                </div>
              </div>

              <div class="skill-item">
                <div class="skill-icon">
                  <i class="bi bi-filetype-html"></i>
                </div>
                <div class="skill-info">
                  <h4>HTML/CSS</h4>
                  <div class="progress">
                    <div class="progress-bar" style="width: 92%">92%</div>
                  </div>
                </div>
              </div>

              <div class="skill-item">
                <div class="skill-icon">
                  <i class="bi bi-database"></i>
                </div>
                <div class="skill-info">
                  <h4>SQL</h4>
                  <div class="progress">
                    <div class="progress-bar" style="width: 88%">88%</div>
                  </div>
                </div>
              </div>

              <div class="skill-item">
                <div class="skill-icon">
                  <i class="bi bi-code"></i>
                </div>
                <div class="skill-info">
                  <h4>React.js</h4>
                  <div class="progress">
                    <div class="progress-bar" style="width: 85%">85%</div>
                  </div>
                </div>
              </div>

              <div class="skill-item">
                <div class="skill-icon">
                  <i class="bi bi-server"></i>
                </div>
                <div class="skill-info">
                  <h4>Node.js</h4>
                  <div class="progress">
                    <div class="progress-bar" style="width: 80%">80%</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Tools & Platforms -->
        <div class="row gy-4 mt-5">
          <div class="col-lg-12" data-aos="fade-up" data-aos-delay="200">
            <h3 class="skills-category-title">
              <i class="bi bi-tools"></i>
              Tools & Platforms
            </h3>
            <div class="tools-grid">
              <div class="tool-badge">
                <i class="bi bi-git"></i>
                <span>Git & GitHub</span>
              </div>
              <div class="tool-badge">
                <i class="bi bi-docker"></i>
                <span>Docker</span>
              </div>
              <div class="tool-badge">
                <i class="bi bi-cloud"></i>
                <span>AWS</span>
              </div>
              <div class="tool-badge">
                <i class="bi bi-database-gear"></i>
                <span>MongoDB</span>
              </div>
              <div class="tool-badge">
                <i class="bi bi-database"></i>
                <span>PostgreSQL</span>
              </div>
              <div class="tool-badge">
                <i class="bi bi-terminal"></i>
                <span>Linux/Unix</span>
              </div>
              <div class="tool-badge">
                <i class="bi bi-code-square"></i>
                <span>VS Code</span>
              </div>
              <div class="tool-badge">
                <i class="bi bi-journal-code"></i>
                <span>Jupyter</span>
              </div>
            </div>
          </div>
        </div>

        <!-- ML/DL/NLP Libraries -->
        <div class="row gy-4 mt-5">
          <div class="col-lg-12" data-aos="fade-up" data-aos-delay="300">
            <h3 class="skills-category-title">
              <i class="bi bi-cpu"></i>
              Machine Learning & AI Libraries
            </h3>
            <div class="ml-skills-grid">
              <div class="ml-skill-card">
                <div class="ml-skill-header">
                  <i class="bi bi-graph-up"></i>
                  <h4>Machine Learning</h4>
                </div>
                <div class="ml-skill-tags">
                  <span class="skill-tag">Scikit-learn</span>
                  <span class="skill-tag">Pandas</span>
                  <span class="skill-tag">NumPy</span>
                  <span class="skill-tag">Matplotlib</span>
                  <span class="skill-tag">Seaborn</span>
                  <span class="skill-tag">Plotly</span>
                </div>
              </div>

              <div class="ml-skill-card">
                <div class="ml-skill-header">
                  <i class="bi bi-brain"></i>
                  <h4>Deep Learning</h4>
                </div>
                <div class="ml-skill-tags">
                  <span class="skill-tag">TensorFlow</span>
                  <span class="skill-tag">Keras</span>
                  <span class="skill-tag">PyTorch</span>
                  <span class="skill-tag">OpenCV</span>
                </div>
              </div>

              <div class="ml-skill-card">
                <div class="ml-skill-header">
                  <i class="bi bi-chat-text"></i>
                  <h4>NLP & LLMs</h4>
                </div>
                <div class="ml-skill-tags">
                  <span class="skill-tag">NLTK</span>
                  <span class="skill-tag">spaCy</span>
                  <span class="skill-tag">Transformers</span>
                  <span class="skill-tag">LangChain</span>
                  <span class="skill-tag">OpenAI API</span>
                </div>
              </div>
            </div>
          </div>
        </div>

      </div>
    </section><!-- /Skills Section -->

  </main>

  <footer id="footer" class="footer position-relative">
    <div class="container">
      <h3 class="sitename">Karan Rajendiran</h3>
      <p>Full-Stack Developer | Data Scientist | AI Engineer</p>
      <div class="social-links d-flex justify-content-center">
        <a href="mailto:<EMAIL>"><i class="bi bi-envelope"></i></a>
        <a href="https://www.linkedin.com/in/karan-rajendiran3152/" target="_blank"><i class="bi bi-linkedin"></i></a>
        <a href="https://github.com/karan3152" target="_blank"><i class="bi bi-github"></i></a>
        <a href="https://kaggle.com/karanrajendiran" target="_blank"><i class="bi bi-trophy"></i></a>
      </div>
      <div class="container">
        <div class="copyright">
          <span>Copyright</span> <strong class="px-1 sitename">Karan Rajendiran</strong> <span>All Rights Reserved</span>
        </div>
      </div>
    </div>
  </footer>

  <!-- Scroll Top -->
  <a href="#" id="scroll-top" class="scroll-top d-flex align-items-center justify-content-center"><i class="bi bi-arrow-up-short"></i></a>

  <!-- Preloader -->
  <div id="preloader"></div>

  <!-- Vendor JS Files -->
  <script src="assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
  <script src="assets/vendor/php-email-form/validate.js"></script>
  <script src="assets/vendor/aos/aos.js"></script>
  <script src="assets/vendor/typed.js/typed.umd.js"></script>
  <script src="assets/vendor/purecounter/purecounter_vanilla.js"></script>
  <script src="assets/vendor/waypoints/noframework.waypoints.js"></script>
  <script src="assets/vendor/swiper/swiper-bundle.min.js"></script>
  <script src="assets/vendor/glightbox/js/glightbox.min.js"></script>
  <script src="assets/vendor/imagesloaded/imagesloaded.pkgd.min.js"></script>
  <script src="assets/vendor/isotope-layout/isotope.pkgd.min.js"></script>

  <!-- Main JS File -->
  <script src="assets/js/main.js"></script>

</body>

</html>
