<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta content="width=device-width, initial-scale=1.0" name="viewport">
  <title>Projects Portfolio - Karan <PERSON></title>
  <meta name="description" content="Portfolio of projects by <PERSON><PERSON> showcasing Software Development, Data Science, and AI/ML Engineering projects with live demos and source code.">
  <meta name="keywords" content="Portfolio, Projects, Software Development, Data Science, AI, Machine Learning, Web Development, Karan Rajendiran">

  <!-- Favicons -->
  <link href="assets/img/favicon.png" rel="icon">
  <link href="assets/img/apple-touch-icon.png" rel="apple-touch-icon">

  <!-- Fonts -->
  <link href="https://fonts.googleapis.com" rel="preconnect">
  <link href="https://fonts.gstatic.com" rel="preconnect" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Raleway:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">

  <!-- Vendor CSS Files -->
  <link href="assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
  <link href="assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
  <link href="assets/vendor/aos/aos.css" rel="stylesheet">
  <link href="assets/vendor/swiper/swiper-bundle.min.css" rel="stylesheet">
  <link href="assets/vendor/glightbox/css/glightbox.min.css" rel="stylesheet">

  <!-- Main CSS File -->
  <link href="assets/css/main.css" rel="stylesheet">

  <!-- =======================================================
  * Template Name: Personal
  * Template URL: https://bootstrapmade.com/personal-free-resume-bootstrap-template/
  * Updated: Mar 05 2025 with Bootstrap v5.3.3
  * Author: BootstrapMade.com
  * License: https://bootstrapmade.com/license/
  ======================================================== -->
</head>

<body class="portfolio-page">

  <header id="header" class="header d-flex align-items-center fixed-top">
    <div class="container-fluid container-xl position-relative d-flex align-items-center justify-content-between">

      <a href="index.html" class="logo d-flex align-items-center">
        <!-- Uncomment the line below if you also wish to use an image logo -->
        <!-- <img src="assets/img/logo.webp" alt=""> -->
        <h1 class="sitename">Karan Rajendiran <img src="assets/img/profile-img.jpg" class="img-fluid" alt=""></h1>
      </a>

      <nav id="navmenu" class="navmenu">
        <ul>
          <li><a href="index.html">Home</a></li>
          <li><a href="about.html">About</a></li>
          <li class="dropdown"><a href="#" class="active"><span>Projects</span> <i class="bi bi-chevron-down toggle-dropdown"></i></a>
            <ul>
              <li><a href="portfolio.html" class="active">All Projects</a></li>
              <li><a href="portfolio.html#software-development">Software Development</a></li>
              <li><a href="portfolio.html#data-science">Data Science</a></li>
              <li><a href="portfolio.html#ai-ml">AI/ML Engineering</a></li>
            </ul>
          </li>
          <li><a href="skills.html">Skills</a></li>
          <li><a href="blog.html">Blog</a></li>
          <li><a href="contact.html">Contact</a></li>
        </ul>
        <i class="mobile-nav-toggle d-xl-none bi bi-list"></i>
      </nav>

    </div>
  </header>

  <main class="main">

    <!-- Page Title -->
    <div class="page-title" data-aos="fade">
      <div class="heading">
        <div class="container">
          <div class="row d-flex justify-content-center text-center">
            <div class="col-lg-8">
              <h1>Projects Portfolio</h1>
              <p class="mb-0">A showcase of my work in Software Development, Data Science, and AI/ML Engineering. Each project demonstrates practical solutions to real-world problems with modern technologies and best practices.</p>
            </div>
          </div>
        </div>
      </div>
      <nav class="breadcrumbs">
        <div class="container">
          <ol>
            <li><a href="index.html">Home</a></li>
            <li class="current">Projects</li>
          </ol>
        </div>
      </nav>
    </div><!-- End Page Title -->

    <!-- Portfolio Section -->
    <section id="portfolio" class="portfolio section">

      <div class="container">

        <div class="isotope-layout" data-default-filter="*" data-layout="masonry" data-sort="original-order">

          <ul class="portfolio-filters isotope-filters" data-aos="fade-up" data-aos-delay="100">
            <li data-filter="*" class="filter-active">All Projects</li>
            <li data-filter=".filter-software">Software Development</li>
            <li data-filter=".filter-data-science">Data Science</li>
            <li data-filter=".filter-ai-ml">AI/ML Engineering</li>
          </ul><!-- End Portfolio Filters -->

          <div class="row gy-4 isotope-container" data-aos="fade-up" data-aos-delay="200">

            <!-- Software Development Projects -->
            <div class="col-lg-4 col-md-6 portfolio-item isotope-item filter-software" id="software-development">
              <div class="portfolio-content h-100">
                <img src="assets/img/portfolio/job-portal.jpg" class="img-fluid" alt="Job Portal Web App">
                <div class="portfolio-info">
                  <h4>Job Portal Web App</h4>
                  <p>Full-stack job board with user roles, dynamic listings, and resume matching</p>
                  <div class="tech-stack">
                    <span class="tech-tag">React</span>
                    <span class="tech-tag">Node.js</span>
                    <span class="tech-tag">MongoDB</span>
                  </div>
                  <a href="assets/img/portfolio/job-portal.jpg" title="Job Portal Web App" data-gallery="portfolio-gallery-software" class="glightbox preview-link"><i class="bi bi-zoom-in"></i></a>
                  <a href="https://github.com/karan3152/job-portal" title="GitHub Repository" class="details-link" target="_blank"><i class="bi bi-github"></i></a>
                  <a href="https://job-portal-demo.netlify.app" title="Live Demo" class="demo-link" target="_blank"><i class="bi bi-box-arrow-up-right"></i></a>
                </div>
              </div>
            </div><!-- End Portfolio Item -->

            <div class="col-lg-4 col-md-6 portfolio-item isotope-item filter-software">
              <div class="portfolio-content h-100">
                <img src="assets/img/portfolio/portfolio-website.jpg" class="img-fluid" alt="Portfolio Website">
                <div class="portfolio-info">
                  <h4>Portfolio Website</h4>
                  <p>Self-designed responsive personal website with modern UI/UX</p>
                  <div class="tech-stack">
                    <span class="tech-tag">HTML</span>
                    <span class="tech-tag">CSS</span>
                    <span class="tech-tag">JavaScript</span>
                    <span class="tech-tag">Bootstrap</span>
                  </div>
                  <a href="assets/img/portfolio/portfolio-website.jpg" title="Portfolio Website" data-gallery="portfolio-gallery-software" class="glightbox preview-link"><i class="bi bi-zoom-in"></i></a>
                  <a href="https://github.com/karan3152/portfolio" title="GitHub Repository" class="details-link" target="_blank"><i class="bi bi-github"></i></a>
                  <a href="https://karanrajendiran.netlify.app" title="Live Demo" class="demo-link" target="_blank"><i class="bi bi-box-arrow-up-right"></i></a>
                </div>
              </div>
            </div><!-- End Portfolio Item -->

            <!-- Data Science Projects -->
            <div class="col-lg-4 col-md-6 portfolio-item isotope-item filter-data-science" id="data-science">
              <div class="portfolio-content h-100">
                <img src="assets/img/portfolio/data-dashboard.jpg" class="img-fluid" alt="Data Insights Dashboard">
                <div class="portfolio-info">
                  <h4>Data Insights Dashboard</h4>
                  <p>Interactive dashboard visualizing COVID trends and sales analytics</p>
                  <div class="tech-stack">
                    <span class="tech-tag">Python</span>
                    <span class="tech-tag">Pandas</span>
                    <span class="tech-tag">Plotly</span>
                    <span class="tech-tag">Dash</span>
                  </div>
                  <a href="assets/img/portfolio/data-dashboard.jpg" title="Data Insights Dashboard" data-gallery="portfolio-gallery-data" class="glightbox preview-link"><i class="bi bi-zoom-in"></i></a>
                  <a href="https://github.com/karan3152/data-dashboard" title="GitHub Repository" class="details-link" target="_blank"><i class="bi bi-github"></i></a>
                  <a href="https://data-insights-dashboard.herokuapp.com" title="Live Demo" class="demo-link" target="_blank"><i class="bi bi-box-arrow-up-right"></i></a>
                </div>
              </div>
            </div><!-- End Portfolio Item -->

            <div class="col-lg-4 col-md-6 portfolio-item isotope-item filter-data-science">
              <div class="portfolio-content h-100">
                <img src="assets/img/portfolio/imdb-analysis.jpg" class="img-fluid" alt="IMDb Dataset Analysis">
                <div class="portfolio-info">
                  <h4>IMDb Dataset Analysis</h4>
                  <p>Exploratory data analysis on film patterns and genre trends</p>
                  <div class="tech-stack">
                    <span class="tech-tag">Python</span>
                    <span class="tech-tag">Jupyter</span>
                    <span class="tech-tag">Matplotlib</span>
                    <span class="tech-tag">Seaborn</span>
                  </div>
                  <a href="assets/img/portfolio/imdb-analysis.jpg" title="IMDb Dataset Analysis" data-gallery="portfolio-gallery-data" class="glightbox preview-link"><i class="bi bi-zoom-in"></i></a>
                  <a href="https://github.com/karan3152/imdb-analysis" title="GitHub Repository" class="details-link" target="_blank"><i class="bi bi-github"></i></a>
                  <a href="https://nbviewer.org/github/karan3152/imdb-analysis/blob/main/analysis.ipynb" title="View Notebook" class="demo-link" target="_blank"><i class="bi bi-journal-code"></i></a>
                </div>
              </div>
            </div><!-- End Portfolio Item -->

            <!-- AI/ML Engineering Projects -->
            <div class="col-lg-4 col-md-6 portfolio-item isotope-item filter-ai-ml" id="ai-ml">
              <div class="portfolio-content h-100">
                <img src="assets/img/portfolio/ai-resume-matcher.jpg" class="img-fluid" alt="AI Resume Matcher">
                <div class="portfolio-info">
                  <h4>AI Resume Matcher</h4>
                  <p>ML model ranking resumes based on job descriptions using NLP</p>
                  <div class="tech-stack">
                    <span class="tech-tag">Python</span>
                    <span class="tech-tag">Scikit-learn</span>
                    <span class="tech-tag">TF-IDF</span>
                    <span class="tech-tag">Flask</span>
                  </div>
                  <a href="assets/img/portfolio/ai-resume-matcher.jpg" title="AI Resume Matcher" data-gallery="portfolio-gallery-ai" class="glightbox preview-link"><i class="bi bi-zoom-in"></i></a>
                  <a href="https://github.com/karan3152/ai-resume-matcher" title="GitHub Repository" class="details-link" target="_blank"><i class="bi bi-github"></i></a>
                  <a href="https://ai-resume-matcher.herokuapp.com" title="Live Demo" class="demo-link" target="_blank"><i class="bi bi-box-arrow-up-right"></i></a>
                </div>
              </div>
            </div><!-- End Portfolio Item -->

            <div class="col-lg-4 col-md-6 portfolio-item isotope-item filter-ai-ml">
              <div class="portfolio-content h-100">
                <img src="assets/img/portfolio/nlp-chatbot.jpg" class="img-fluid" alt="NLP Chatbot">
                <div class="portfolio-info">
                  <h4>AI-Powered Chatbot</h4>
                  <p>Customer support bot using NLP and machine learning</p>
                  <div class="tech-stack">
                    <span class="tech-tag">Python</span>
                    <span class="tech-tag">NLTK</span>
                    <span class="tech-tag">Rasa</span>
                    <span class="tech-tag">TensorFlow</span>
                  </div>
                  <a href="assets/img/portfolio/nlp-chatbot.jpg" title="NLP Chatbot" data-gallery="portfolio-gallery-ai" class="glightbox preview-link"><i class="bi bi-zoom-in"></i></a>
                  <a href="https://github.com/karan3152/nlp-chatbot" title="GitHub Repository" class="details-link" target="_blank"><i class="bi bi-github"></i></a>
                  <a href="https://nlp-chatbot-demo.herokuapp.com" title="Live Demo" class="demo-link" target="_blank"><i class="bi bi-box-arrow-up-right"></i></a>
                </div>
              </div>
            </div><!-- End Portfolio Item -->

            <div class="col-lg-4 col-md-6 portfolio-item isotope-item filter-ai-ml">
              <div class="portfolio-content h-100">
                <img src="assets/img/portfolio/document-automation.jpg" class="img-fluid" alt="Document Automation">
                <div class="portfolio-info">
                  <h4>Document Automation with LangChain</h4>
                  <p>Automated PDF interaction using NLP - 40% efficiency improvement</p>
                  <div class="tech-stack">
                    <span class="tech-tag">Python</span>
                    <span class="tech-tag">LangChain</span>
                    <span class="tech-tag">OpenAI</span>
                    <span class="tech-tag">Streamlit</span>
                  </div>
                  <a href="assets/img/portfolio/document-automation.jpg" title="Document Automation" data-gallery="portfolio-gallery-ai" class="glightbox preview-link"><i class="bi bi-zoom-in"></i></a>
                  <a href="https://github.com/karan3152/document-automation" title="GitHub Repository" class="details-link" target="_blank"><i class="bi bi-github"></i></a>
                  <a href="https://document-automation.streamlit.app" title="Live Demo" class="demo-link" target="_blank"><i class="bi bi-box-arrow-up-right"></i></a>
                </div>
              </div>
            </div><!-- End Portfolio Item -->

            <!-- Additional Software Development Project -->
            <div class="col-lg-4 col-md-6 portfolio-item isotope-item filter-software">
              <div class="portfolio-content h-100">
                <img src="assets/img/portfolio/e-commerce-app.jpg" class="img-fluid" alt="E-commerce Platform">
                <div class="portfolio-info">
                  <h4>E-commerce Platform</h4>
                  <p>Full-stack e-commerce solution with payment integration</p>
                  <div class="tech-stack">
                    <span class="tech-tag">React</span>
                    <span class="tech-tag">Express</span>
                    <span class="tech-tag">PostgreSQL</span>
                    <span class="tech-tag">Stripe</span>
                  </div>
                  <a href="assets/img/portfolio/e-commerce-app.jpg" title="E-commerce Platform" data-gallery="portfolio-gallery-software" class="glightbox preview-link"><i class="bi bi-zoom-in"></i></a>
                  <a href="https://github.com/karan3152/e-commerce-platform" title="GitHub Repository" class="details-link" target="_blank"><i class="bi bi-github"></i></a>
                  <a href="https://e-commerce-demo.netlify.app" title="Live Demo" class="demo-link" target="_blank"><i class="bi bi-box-arrow-up-right"></i></a>
                </div>
              </div>
            </div><!-- End Portfolio Item -->

            <!-- Additional Data Science Project -->
            <div class="col-lg-4 col-md-6 portfolio-item isotope-item filter-data-science">
              <div class="portfolio-content h-100">
                <img src="assets/img/portfolio/predictive-analytics.jpg" class="img-fluid" alt="Predictive Analytics">
                <div class="portfolio-info">
                  <h4>Sales Forecasting Model</h4>
                  <p>Time series analysis for business sales prediction</p>
                  <div class="tech-stack">
                    <span class="tech-tag">Python</span>
                    <span class="tech-tag">Prophet</span>
                    <span class="tech-tag">Scikit-learn</span>
                    <span class="tech-tag">Streamlit</span>
                  </div>
                  <a href="assets/img/portfolio/predictive-analytics.jpg" title="Sales Forecasting Model" data-gallery="portfolio-gallery-data" class="glightbox preview-link"><i class="bi bi-zoom-in"></i></a>
                  <a href="https://github.com/karan3152/sales-forecasting" title="GitHub Repository" class="details-link" target="_blank"><i class="bi bi-github"></i></a>
                  <a href="https://sales-forecasting.streamlit.app" title="Live Demo" class="demo-link" target="_blank"><i class="bi bi-box-arrow-up-right"></i></a>
                </div>
              </div>
            </div><!-- End Portfolio Item -->

          </div><!-- End Portfolio Container -->

        </div>

      </div>

    </section><!-- /Portfolio Section -->

  </main>

  <footer id="footer" class="footer dark-background">
    <div class="container">
      <h3 class="sitename">Personal</h3>
      <p>Et aut eum quis fuga eos sunt ipsa nihil. Labore corporis magni eligendi fuga maxime saepe commodi placeat.</p>
      <div class="social-links d-flex justify-content-center">
        <a href=""><i class="bi bi-twitter-x"></i></a>
        <a href=""><i class="bi bi-facebook"></i></a>
        <a href=""><i class="bi bi-instagram"></i></a>
        <a href=""><i class="bi bi-skype"></i></a>
        <a href=""><i class="bi bi-linkedin"></i></a>
      </div>
      <div class="container">
        <div class="copyright">
          <span>Copyright</span> <strong class="px-1 sitename">Personal</strong> <span>All Rights Reserved</span>
        </div>
        <div class="credits">
          <!-- All the links in the footer should remain intact. -->
          <!-- You can delete the links only if you've purchased the pro version. -->
          <!-- Licensing information: https://bootstrapmade.com/license/ -->
          <!-- Purchase the pro version with working PHP/AJAX contact form: [buy-url] -->
          Designed by <a href="https://bootstrapmade.com/">BootstrapMade</a>
        </div>
      </div>
    </div>
  </footer>

  <!-- Scroll Top -->
  <a href="#" id="scroll-top" class="scroll-top d-flex align-items-center justify-content-center"><i class="bi bi-arrow-up-short"></i></a>

  <!-- Preloader -->
  <div id="preloader"></div>

  <!-- Vendor JS Files -->
  <script src="assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
  <script src="assets/vendor/php-email-form/validate.js"></script>
  <script src="assets/vendor/aos/aos.js"></script>
  <script src="assets/vendor/typed.js/typed.umd.js"></script>
  <script src="assets/vendor/purecounter/purecounter_vanilla.js"></script>
  <script src="assets/vendor/waypoints/noframework.waypoints.js"></script>
  <script src="assets/vendor/swiper/swiper-bundle.min.js"></script>
  <script src="assets/vendor/glightbox/js/glightbox.min.js"></script>
  <script src="assets/vendor/imagesloaded/imagesloaded.pkgd.min.js"></script>
  <script src="assets/vendor/isotope-layout/isotope.pkgd.min.js"></script>

  <!-- Main JS File -->
  <script src="assets/js/main.js"></script>

</body>

</html>