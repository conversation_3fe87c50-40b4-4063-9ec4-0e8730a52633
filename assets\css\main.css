/**
* Template Name: Personal
* Template URL: https://bootstrapmade.com/personal-free-resume-bootstrap-template/
* Updated: Mar 05 2025 with Bootstrap v5.3.3
* Author: BootstrapMade.com
* License: https://bootstrapmade.com/license/
*/

/*--------------------------------------------------------------
# Font & Color Variables
# Help: https://bootstrapmade.com/color-system/
--------------------------------------------------------------*/
/* Fonts */
:root {
  --default-font: "Roboto",  system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", "Liberation Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --heading-font: "Raleway",  sans-serif;
  --nav-font: "Poppins",  sans-serif;
}

/* Global Colors - The following color variables are used throughout the website. Updating them here will change the color scheme of the entire website */
:root {
  --background-color: #ffffff; /* Background color for the entire website, including individual sections */
  --default-color: #333333; /* Default color used for the majority of the text content across the entire website */
  --heading-color: #000000; /* Color for headings, subheadings and title throughout the website */
  --accent-color: #18d26e; /* Accent color that represents your brand on the website. It's used for buttons, links, and other elements that need to stand out */
  --surface-color: #f8f9fa; /* The surface color is used as a background of boxed elements within sections, such as cards, icon boxes, or other elements that require a visual separation from the global background. */
  --contrast-color: #ffffff; /* Contrast color for text, ensuring readability against backgrounds of accent, heading, or default colors. */
}

/* Nav Menu Colors - The following color variables are used specifically for the navigation menu. They are separate from the global colors to allow for more customization options */
:root {
  --nav-color: #333333;  /* The default color of the main navmenu links */
  --nav-hover-color: #18d26e; /* Applied to main navmenu links when they are hovered over or active */
  --nav-mobile-background-color: #f8f9fa; /* Used as the background color for mobile navigation menu */
  --nav-dropdown-background-color: #f8f9fa; /* Used as the background color for dropdown items that appear when hovering over primary navigation items */
  --nav-dropdown-color: #333333; /* Used for navigation links of the dropdown items in the navigation menu. */
  --nav-dropdown-hover-color: #18d26e; /* Similar to --nav-hover-color, this color is applied to dropdown navigation links when they are hovered over. */
}

/* Color Presets - These classes override global colors when applied to any section or element, providing reuse of the sam color scheme. */

.light-background {
  --background-color: #f8f9fa;
  --surface-color: #e9ecef;
}

.dark-background {
  --background-color: rgba(240, 240, 240, 0.975);
  --default-color: #333333;
  --heading-color: #000000;
  --surface-color: #e9ecef;
  --contrast-color: #ffffff;
}

/* Smooth scroll */
:root {
  scroll-behavior: smooth;
}

/*--------------------------------------------------------------
# General Styling & Shared Classes
--------------------------------------------------------------*/
body {
  color: var(--default-color);
  background-color: var(--background-color);
  font-family: var(--default-font);
}

a {
  color: var(--accent-color);
  text-decoration: none;
  transition: 0.3s;
}

a:hover {
  color: color-mix(in srgb, var(--accent-color), transparent 25%);
  text-decoration: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: var(--heading-color);
  font-family: var(--heading-font);
}

/* PHP Email Form Messages
------------------------------*/
.php-email-form .error-message {
  display: none;
  background: #df1529;
  color: #ffffff;
  text-align: left;
  padding: 15px;
  margin-bottom: 24px;
  font-weight: 600;
}

.php-email-form .sent-message {
  display: none;
  color: #ffffff;
  background: #059652;
  text-align: center;
  padding: 15px;
  margin-bottom: 24px;
  font-weight: 600;
}

.php-email-form .loading {
  display: none;
  background: var(--surface-color);
  text-align: center;
  padding: 15px;
  margin-bottom: 24px;
}

.php-email-form .loading:before {
  content: "";
  display: inline-block;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  margin: 0 10px -6px 0;
  border: 3px solid var(--accent-color);
  border-top-color: var(--surface-color);
  animation: php-email-form-loading 1s linear infinite;
}

@keyframes php-email-form-loading {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/*--------------------------------------------------------------
# Global Header
--------------------------------------------------------------*/
.header {
  --background-color: rgba(255, 255, 255, 0);
  color: var(--default-color);
  background-color: var(--background-color);
  padding: 15px 0;
  transition: all 0.5s;
  z-index: 997;
}

.header .logo {
  line-height: 1;
}

.header .logo img {
  max-height: 32px;
  margin-right: 8px;
}

.header .logo h1 {
  font-size: 30px;
  margin: 0;
  font-weight: 700;
  color: var(--heading-color);
}

.scrolled .header {
  box-shadow: 0px 0 18px rgba(0, 0, 0, 0.1);
}

/* Global Header on Scroll
------------------------------*/
.scrolled .header {
  --background-color: rgba(255, 255, 255, 0.95);
}

/*--------------------------------------------------------------
# Navigation Menu
--------------------------------------------------------------*/
/* Desktop Navigation */
@media (min-width: 1200px) {
  .navmenu {
    padding: 0;
  }

  .navmenu ul {
    margin: 0;
    padding: 0;
    display: flex;
    list-style: none;
    align-items: center;
  }

  .navmenu li {
    position: relative;
  }

  .navmenu>ul>li {
    white-space: nowrap;
    padding: 15px 14px;
  }

  .navmenu>ul>li:last-child {
    padding-right: 0;
  }

  .navmenu a,
  .navmenu a:focus {
    color: var(--nav-color);
    font-size: 15px;
    padding: 0 2px;
    font-family: var(--nav-font);
    font-weight: 400;
    display: flex;
    align-items: center;
    justify-content: space-between;
    white-space: nowrap;
    transition: 0.3s;
    position: relative;
  }

  .navmenu a i,
  .navmenu a:focus i {
    font-size: 12px;
    line-height: 0;
    margin-left: 5px;
    transition: 0.3s;
  }

  .navmenu>ul>li>a:before {
    content: "";
    position: absolute;
    height: 2px;
    bottom: -6px;
    left: 0;
    background-color: var(--nav-hover-color);
    visibility: hidden;
    width: 0px;
    transition: all 0.3s ease-in-out 0s;
  }

  .navmenu a:hover:before,
  .navmenu li:hover>a:before,
  .navmenu .active:before {
    visibility: visible;
    width: 25px;
  }

  .navmenu li:hover>a,
  .navmenu .active,
  .navmenu .active:focus {
    color: var(--nav-hover-color);
  }

  .navmenu .dropdown ul {
    margin: 0;
    padding: 10px 0;
    background: var(--nav-dropdown-background-color);
    display: block;
    position: absolute;
    visibility: hidden;
    left: 14px;
    top: 130%;
    opacity: 0;
    transition: 0.3s;
    border-radius: 4px;
    z-index: 99;
    box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.1);
  }

  .navmenu .dropdown ul li {
    min-width: 200px;
  }

  .navmenu .dropdown ul a {
    padding: 10px 20px;
    font-size: 15px;
    text-transform: none;
    color: var(--nav-dropdown-color);
  }

  .navmenu .dropdown ul a i {
    font-size: 12px;
  }

  .navmenu .dropdown ul a:hover,
  .navmenu .dropdown ul .active:hover,
  .navmenu .dropdown ul li:hover>a {
    color: var(--nav-dropdown-hover-color);
  }

  .navmenu .dropdown:hover>ul {
    opacity: 1;
    top: 100%;
    visibility: visible;
  }

  .navmenu .dropdown .dropdown ul {
    top: 0;
    left: -90%;
    visibility: hidden;
  }

  .navmenu .dropdown .dropdown:hover>ul {
    opacity: 1;
    top: 0;
    left: -100%;
    visibility: visible;
  }
}

/* Mobile Navigation */
@media (max-width: 1199px) {
  .mobile-nav-toggle {
    color: var(--nav-color);
    font-size: 28px;
    line-height: 0;
    margin-right: 10px;
    cursor: pointer;
    transition: color 0.3s;
  }

  .navmenu {
    padding: 0;
    z-index: 9997;
  }

  .navmenu ul {
    display: none;
    list-style: none;
    position: absolute;
    inset: 60px 20px 20px 20px;
    padding: 10px 0;
    margin: 0;
    border-radius: 6px;
    background-color: var(--nav-mobile-background-color);
    border: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
    box-shadow: none;
    overflow-y: auto;
    transition: 0.3s;
    z-index: 9998;
  }

  .navmenu a,
  .navmenu a:focus {
    color: var(--nav-dropdown-color);
    padding: 10px 20px;
    font-family: var(--nav-font);
    font-size: 17px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: space-between;
    white-space: nowrap;
    transition: 0.3s;
  }

  .navmenu a i,
  .navmenu a:focus i {
    font-size: 12px;
    line-height: 0;
    margin-left: 5px;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: 0.3s;
    background-color: color-mix(in srgb, var(--accent-color), transparent 90%);
  }

  .navmenu a i:hover,
  .navmenu a:focus i:hover {
    background-color: var(--accent-color);
    color: var(--contrast-color);
  }

  .navmenu a:hover,
  .navmenu .active,
  .navmenu .active:focus {
    color: var(--nav-dropdown-hover-color);
  }

  .navmenu .active i,
  .navmenu .active:focus i {
    background-color: var(--accent-color);
    color: var(--contrast-color);
    transform: rotate(180deg);
  }

  .navmenu .dropdown ul {
    position: static;
    display: none;
    z-index: 99;
    padding: 10px 0;
    margin: 10px 20px;
    background-color: var(--nav-dropdown-background-color);
    transition: all 0.5s ease-in-out;
  }

  .navmenu .dropdown ul ul {
    background-color: rgba(248, 249, 250, 0.9);
  }

  .navmenu .dropdown>.dropdown-active {
    display: block;
    background-color: rgba(248, 249, 250, 0.5);
  }

  .mobile-nav-active {
    overflow: hidden;
  }

  .mobile-nav-active .mobile-nav-toggle {
    color: #fff;
    position: absolute;
    font-size: 32px;
    top: 15px;
    right: 15px;
    margin-right: 0;
    z-index: 9999;
  }

  .mobile-nav-active .navmenu {
    position: fixed;
    overflow: hidden;
    inset: 0;
    background: rgba(248, 249, 250, 0.95);
    transition: 0.3s;
  }

  .mobile-nav-active .navmenu>ul {
    display: block;
  }
}

/*--------------------------------------------------------------
# Global Footer
--------------------------------------------------------------*/
.footer {
  color: var(--default-color);
  background-color: var(--background-color);
  font-size: 14px;
  text-align: center;
  padding: 30px 0;
  position: relative;
}

.footer h3 {
  font-size: 36px;
  font-weight: 700;
  position: relative;
  padding: 0;
  margin: 0 0 15px 0;
}

.footer p {
  font-size: 15;
  font-style: italic;
  padding: 0;
  margin: 0 0 30px 0;
}

.footer .social-links {
  margin: 0 0 30px 0;
}

.footer .social-links a {
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--accent-color);
  color: var(--contrast-color);
  line-height: 1;
  margin: 0 4px;
  border-radius: 50%;
  text-align: center;
  width: 36px;
  height: 36px;
  transition: 0.3s;
}

.footer .social-links a:hover {
  background: color-mix(in srgb, var(--accent-color), transparent 20%);
  text-decoration: none;
}

.footer .copyright {
  padding-top: 25px;
  border-top: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
}

.footer .credits {
  font-size: 13px;
  padding-top: 5px;
}

/*--------------------------------------------------------------
# Preloader
--------------------------------------------------------------*/
#preloader {
  position: fixed;
  inset: 0;
  z-index: 999999;
  overflow: hidden;
  background: var(--background-color);
  transition: all 0.6s ease-out;
}

#preloader:before {
  content: "";
  position: fixed;
  top: calc(50% - 30px);
  left: calc(50% - 30px);
  border: 6px solid #333333;
  border-color: var(--accent-color) transparent var(--accent-color) transparent;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  animation: animate-preloader 1.5s linear infinite;
}

@keyframes animate-preloader {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/*--------------------------------------------------------------
# Scroll Top Button
--------------------------------------------------------------*/
.scroll-top {
  position: fixed;
  visibility: hidden;
  opacity: 0;
  right: 15px;
  bottom: -15px;
  z-index: 99999;
  background-color: var(--accent-color);
  width: 44px;
  height: 44px;
  border-radius: 50px;
  transition: all 0.4s;
}

.scroll-top i {
  font-size: 24px;
  color: var(--contrast-color);
  line-height: 0;
}

.scroll-top:hover {
  background-color: color-mix(in srgb, var(--accent-color), transparent 20%);
  color: var(--contrast-color);
}

.scroll-top.active {
  visibility: visible;
  opacity: 1;
  bottom: 15px;
}

/*--------------------------------------------------------------
# Disable aos animation delay on mobile devices
--------------------------------------------------------------*/
@media screen and (max-width: 768px) {
  [data-aos-delay] {
    transition-delay: 0 !important;
  }
}

/*--------------------------------------------------------------
# Global Page Titles & Breadcrumbs
--------------------------------------------------------------*/
.page-title {
  color: var(--default-color);
  background-color: var(--background-color);
  position: relative;
}

.page-title .heading {
  padding: 160px 0 80px 0;
  border-top: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
}

.page-title .heading h1 {
  font-size: 38px;
  font-weight: 700;
}

.page-title nav {
  background-color: color-mix(in srgb, var(--default-color), transparent 90%);
  padding: 20px 0;
}

.page-title nav ol {
  display: flex;
  flex-wrap: wrap;
  list-style: none;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.page-title nav ol li+li {
  padding-left: 10px;
}

.page-title nav ol li+li::before {
  content: "/";
  display: inline-block;
  padding-right: 10px;
  color: color-mix(in srgb, var(--default-color), transparent 70%);
}

/*--------------------------------------------------------------
# Global Sections
--------------------------------------------------------------*/
section,
.section {
  color: var(--default-color);
  background-color: var(--background-color);
  padding: 60px 0;
  scroll-margin-top: 100px;
  overflow: clip;
}

@media (max-width: 1199px) {

  section,
  .section {
    scroll-margin-top: 66px;
  }
}

/*--------------------------------------------------------------
# Global Section Titles
--------------------------------------------------------------*/
.section-title {
  padding-bottom: 60px;
  position: relative;
}

.section-title h2 {
  font-size: 14px;
  font-weight: 500;
  padding: 0;
  line-height: 1px;
  margin: 0;
  letter-spacing: 1.5px;
  text-transform: uppercase;
  color: color-mix(in srgb, var(--default-color), transparent 50%);
  position: relative;
}

.section-title h2::after {
  content: "";
  width: 120px;
  height: 1px;
  display: inline-block;
  background: var(--accent-color);
  margin: 4px 10px;
}

.section-title div {
  color: var(--heading-color);
  margin: 0;
  margin: 0;
  font-size: 28px;
  font-weight: 700;
  text-transform: uppercase;
  font-family: var(--heading-font);
}

/*--------------------------------------------------------------
# Hero Section
--------------------------------------------------------------*/
.hero {
  width: 100%;
  min-height: 100vh;
  position: relative;
  padding: 80px 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero img {
  position: absolute;
  inset: 0;
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 1;
}

.hero .container {
  position: relative;
  z-index: 3;
}

.hero h2 {
  margin: 0;
  font-size: 64px;
  font-weight: 700;
}

.hero p {
  margin: 5px 0 0 0;
  font-size: 26px;
}

.hero p span {
  letter-spacing: 1px;
  border-bottom: 2px solid var(--accent-color);
}

.hero .social-links {
  margin-top: 25px;
}

.hero .social-links a {
  background-color: color-mix(in srgb, var(--default-color), transparent 90%);
  color: var(--default-color);
  font-size: 14px;
  width: 40px;
  height: 40px;
  border-radius: 50px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  transition: 0.3s;
}

.hero .social-links a:hover {
  background-color: var(--accent-color);
}

/* Profile Logo in Header */
.profile-logo {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  margin-left: 10px;
  border: 2px solid var(--accent-color);
  transition: transform 0.3s ease;
}

.profile-logo:hover {
  transform: scale(1.1);
}

/* Hero Section Restructured */
.hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.hero-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -2;
}

.hero-background {
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0.1;
}

.hero-content {
  z-index: 2;
  padding-right: 30px;
}

.hero-name {
  font-size: 3.5rem;
  font-weight: 700;
  color: var(--heading-color);
  margin-bottom: 15px;
  line-height: 1.2;
}

.hero-tagline {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--accent-color);
  margin-bottom: 20px;
  line-height: 1.3;
}

.hero-description {
  font-size: 1.1rem;
  color: var(--default-color);
  margin-bottom: 30px;
  line-height: 1.6;
}

.hero-highlights {
  margin: 30px 0;
}

.highlight-item {
  text-align: center;
  padding: 20px 15px;
  background: var(--surface-color);
  border-radius: 15px;
  margin-bottom: 20px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border: 1px solid color-mix(in srgb, var(--accent-color), transparent 80%);
}

.highlight-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: var(--accent-color);
}

.highlight-item i {
  font-size: 28px;
  color: var(--accent-color);
  margin-bottom: 12px;
  display: block;
}

.highlight-item span {
  font-size: 14px;
  font-weight: 600;
  color: var(--heading-color);
}

/* Hero Image Styling */
.hero-image-col {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.hero-image-container {
  position: relative;
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
}

.hero-profile-image {
  width: 100%;
  height: auto;
  border-radius: 15px;
  object-fit: cover;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  border: 3px solid var(--surface-color);
  position: relative;
  z-index: 2;
}

.hero-profile-image:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 45px rgba(0, 0, 0, 0.25);
}

.image-decoration {
  position: absolute;
  top: -15px;
  left: -15px;
  right: -15px;
  bottom: -15px;
  border: 2px solid var(--accent-color);
  border-radius: 20px;
  z-index: 1;
  opacity: 0.7;
}

/* Social Links Enhancement */
.hero .social-links {
  margin: 30px 0;
  display: flex;
  gap: 15px;
}

.hero .social-links a {
  background-color: var(--surface-color);
  color: var(--default-color);
  font-size: 18px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.hero .social-links a:hover {
  background-color: var(--accent-color);
  color: white;
  transform: translateY(-5px);
  border-color: var(--accent-color);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.hero-cta {
  margin-top: 40px;
}

.hero-cta .btn {
  padding: 15px 35px;
  font-weight: 600;
  border-radius: 30px;
  transition: all 0.3s ease;
  font-size: 16px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.hero-cta .btn-primary {
  background: linear-gradient(135deg, var(--accent-color), color-mix(in srgb, var(--accent-color), blue 20%));
  border: none;
  color: white;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.hero-cta .btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.hero-cta .btn-outline-primary {
  border: 2px solid var(--accent-color);
  color: var(--accent-color);
  background: transparent;
  backdrop-filter: blur(10px);
}

.hero-cta .btn-outline-primary:hover {
  background-color: var(--accent-color);
  color: white;
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .hero-name {
    font-size: 3rem;
  }

  .hero-tagline {
    font-size: 1.3rem;
  }

  .hero-content {
    padding-right: 15px;
  }
}

@media (max-width: 992px) {
  .hero-name {
    font-size: 2.5rem;
  }

  .hero-tagline {
    font-size: 1.2rem;
  }

  .hero-description {
    font-size: 1rem;
  }

  .hero-image-container {
    max-width: 350px;
    margin-top: 30px;
  }

  .hero-content {
    text-align: center;
    padding-right: 0;
  }

  .hero .social-links {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .hero {
    min-height: auto;
    padding: 100px 0 50px;
  }

  .hero-name {
    font-size: 2.2rem;
    margin-bottom: 10px;
  }

  .hero-tagline {
    font-size: 1.1rem;
    margin-bottom: 15px;
  }

  .hero-description {
    font-size: 0.95rem;
    margin-bottom: 25px;
  }

  .hero-highlights .row {
    margin: 0 -5px;
  }

  .hero-highlights .col-md-4 {
    padding: 0 5px;
    margin-bottom: 15px;
  }

  .highlight-item {
    padding: 15px 10px;
    margin-bottom: 10px;
  }

  .highlight-item i {
    font-size: 24px;
    margin-bottom: 8px;
  }

  .highlight-item span {
    font-size: 13px;
  }

  .hero-image-container {
    max-width: 280px;
    margin: 20px auto 30px;
  }

  .hero-profile-image {
    border-radius: 12px;
    border-width: 2px;
  }

  .image-decoration {
    top: -12px;
    left: -12px;
    right: -12px;
    bottom: -12px;
    border-radius: 15px;
  }

  .hero .social-links {
    margin: 25px 0;
    gap: 12px;
  }

  .hero .social-links a {
    width: 45px;
    height: 45px;
    font-size: 16px;
  }

  .hero-cta {
    margin-top: 30px;
  }

  .hero-cta .btn {
    display: block;
    width: 100%;
    margin-bottom: 15px;
    padding: 12px 25px;
    font-size: 14px;
  }

  .hero-cta .ms-3 {
    margin-left: 0 !important;
  }

  .profile-logo {
    width: 35px;
    height: 35px;
    margin-left: 8px;
  }
}

@media (max-width: 576px) {
  .hero-name {
    font-size: 1.8rem;
  }

  .hero-tagline {
    font-size: 1rem;
  }

  .hero-description {
    font-size: 0.9rem;
  }

  .hero-image-container {
    max-width: 250px;
  }

  .highlight-item {
    padding: 12px 8px;
  }

  .highlight-item i {
    font-size: 20px;
  }

  .highlight-item span {
    font-size: 12px;
  }

  .hero .social-links a {
    width: 40px;
    height: 40px;
    font-size: 14px;
  }

  .profile-logo {
    width: 30px;
    height: 30px;
  }
}

/*--------------------------------------------------------------
# About Section
--------------------------------------------------------------*/
.about .content h2 {
  font-weight: 700;
  font-size: 24px;
}

.about .content ul {
  list-style: none;
  padding: 0;
}

.about .content ul li {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.about .content ul strong {
  margin-right: 10px;
}

.about .content ul i {
  font-size: 16px;
  margin-right: 5px;
  color: var(--accent-color);
  line-height: 0;
}

/*--------------------------------------------------------------
# Stats Section
--------------------------------------------------------------*/
.stats i {
  background-color: var(--surface-color);
  color: var(--accent-color);
  box-shadow: 0px 2px 25px rgba(0, 0, 0, 0.1);
  width: 54px;
  height: 54px;
  font-size: 24px;
  border-radius: 50px;
  border: 2px solid var(--background-color);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 1;
}

.stats .stats-item {
  background-color: var(--surface-color);
  margin-top: -27px;
  padding: 30px 30px 25px 30px;
  width: 100%;
  position: relative;
  text-align: center;
  box-shadow: 0px 2px 35px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  z-index: 0;
}

.stats .stats-item span {
  font-size: 36px;
  display: block;
  font-weight: 700;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
}

.stats .stats-item p {
  padding: 0;
  margin: 0;
  font-family: var(--heading-font);
  font-size: 16px;
}

/*--------------------------------------------------------------
# Skills Section
--------------------------------------------------------------*/
.skills .progress {
  height: 60px;
  display: block;
  background: none;
  border-radius: 0;
}

.skills .progress .skill {
  color: var(--heading-color);
  padding: 0;
  margin: 0 0 6px 0;
  text-transform: uppercase;
  display: block;
  font-weight: 600;
  font-family: var(--heading-font);
}

.skills .progress .skill .val {
  float: right;
  font-style: normal;
}

.skills .progress-bar-wrap {
  background: color-mix(in srgb, var(--default-color), transparent 90%);
  height: 10px;
}

.skills .progress-bar {
  width: 1px;
  height: 10px;
  transition: 0.9s;
  background-color: var(--accent-color);
}

/*--------------------------------------------------------------
# Interests Section
--------------------------------------------------------------*/
.interests .features-item {
  background-color: var(--surface-color);
  display: flex;
  align-items: center;
  padding: 20px;
  transition: 0.3s;
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
  position: relative;
}

.interests .features-item i {
  font-size: 32px;
  padding-right: 10px;
  line-height: 0;
}

.interests .features-item h3 {
  font-weight: 700;
  margin: 0;
  padding: 0;
  line-height: 1;
  font-size: 16px;
}

.interests .features-item h3 a {
  color: var(--heading-color);
  transition: 0.3s;
}

.interests .features-item:hover {
  border-color: var(--accent-color);
}

.interests .features-item:hover h3 a {
  color: var(--accent-color);
}

/*--------------------------------------------------------------
# Testimonials Section
--------------------------------------------------------------*/
.testimonials .testimonials-carousel,
.testimonials .testimonials-slider {
  overflow: hidden;
}

.testimonials .testimonial-item {
  box-sizing: content-box;
  min-height: 320px;
}

.testimonials .testimonial-item .testimonial-img {
  width: 90px;
  border-radius: 50%;
  margin: -40px 0 0 40px;
  position: relative;
  z-index: 2;
  border: 6px solid var(--background-color);
}

.testimonials .testimonial-item h3 {
  font-size: 18px;
  font-weight: bold;
  margin: 10px 0 5px 45px;
}

.testimonials .testimonial-item h4 {
  font-size: 14px;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  margin: 0 0 0 45px;
}

.testimonials .testimonial-item .quote-icon-left,
.testimonials .testimonial-item .quote-icon-right {
  color: color-mix(in srgb, var(--accent-color), transparent 50%);
  font-size: 26px;
  line-height: 0;
}

.testimonials .testimonial-item .quote-icon-left {
  display: inline-block;
  left: -5px;
  position: relative;
}

.testimonials .testimonial-item .quote-icon-right {
  display: inline-block;
  right: -5px;
  position: relative;
  top: 10px;
  transform: scale(-1, -1);
}

.testimonials .testimonial-item p {
  font-style: italic;
  margin: 0 15px 0 15px;
  padding: 20px 20px 60px 20px;
  background: var(--surface-color);
  border-radius: 6px;
  position: relative;
  z-index: 1;
}

.testimonials .swiper-wrapper {
  height: auto;
}

.testimonials .swiper-pagination {
  margin-top: 20px;
  position: relative;
}

.testimonials .swiper-pagination .swiper-pagination-bullet {
  width: 12px;
  height: 12px;
  background-color: var(--background-color);
  opacity: 1;
  border: 1px solid var(--accent-color);
}

.testimonials .swiper-pagination .swiper-pagination-bullet-active {
  background-color: var(--accent-color);
}

/*--------------------------------------------------------------
# Resume Section
--------------------------------------------------------------*/
.resume .resume-title {
  color: var(--heading-color);
  font-size: 26px;
  font-weight: 700;
  margin-top: 20px;
  margin-bottom: 20px;
}

.resume .resume-item {
  padding: 0 0 20px 20px;
  margin-top: -2px;
  border-left: 2px solid var(--accent-color);
  position: relative;
}

.resume .resume-item h4 {
  line-height: 18px;
  font-size: 18px;
  font-weight: 600;
  text-transform: uppercase;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  margin-bottom: 10px;
}

.resume .resume-item h5 {
  font-size: 16px;
  padding: 5px 15px;
  display: inline-block;
  font-weight: 600;
  margin-bottom: 10px;
}

.resume .resume-item ul {
  padding-left: 20px;
}

.resume .resume-item ul li {
  padding-bottom: 10px;
}

.resume .resume-item:last-child {
  padding-bottom: 0;
}

.resume .resume-item::before {
  content: "";
  position: absolute;
  width: 16px;
  height: 16px;
  border-radius: 50px;
  left: -9px;
  top: 0;
  background: var(--background-color);
  border: 2px solid var(--accent-color);
}

/*--------------------------------------------------------------
# Services Section
--------------------------------------------------------------*/
.services .service-item {
  background-color: var(--surface-color);
  text-align: center;
  padding: 80px 20px;
  transition: all ease-in-out 0.3s;
  height: 100%;
}

.services .service-item .icon {
  margin: 0 auto;
  width: 64px;
  height: 64px;
  background: var(--accent-color);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  transition: 0.3s;
  transform-style: preserve-3d;
}

.services .service-item .icon i {
  color: var(--contrast-color);
  font-size: 28px;
  transition: ease-in-out 0.3s;
}

.services .service-item .icon::before {
  position: absolute;
  content: "";
  left: -8px;
  top: -8px;
  height: 100%;
  width: 100%;
  background: color-mix(in srgb, var(--accent-color), transparent 80%);
  border-radius: 5px;
  transition: all 0.3s ease-out 0s;
  transform: translateZ(-1px);
}

.services .service-item h3 {
  font-weight: 700;
  margin: 10px 0 15px 0;
  font-size: 22px;
}

.services .service-item p {
  line-height: 24px;
  font-size: 14px;
  margin-bottom: 0;
}

.services .service-item:hover {
  background: var(--accent-color);
  border-color: var(--accent-color);
}

.services .service-item:hover .icon {
  background: var(--surface-color);
}

.services .service-item:hover .icon i {
  color: var(--accent-color);
}

.services .service-item:hover .icon::before {
  background: color-mix(in srgb, var(--background-color), transparent 70%);
}

.services .service-item:hover h3,
.services .service-item:hover p {
  color: var(--contrast-color);
}

/*--------------------------------------------------------------
# Service Details Section
--------------------------------------------------------------*/
.service-details .services-list {
  background-color: var(--surface-color);
  padding: 10px 30px;
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
  margin-bottom: 20px;
}

.service-details .services-list a {
  display: block;
  line-height: 1;
  padding: 8px 0 8px 15px;
  border-left: 3px solid color-mix(in srgb, var(--default-color), transparent 70%);
  margin: 20px 0;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  transition: 0.3s;
}

.service-details .services-list a.active {
  color: var(--heading-color);
  font-weight: 700;
  border-color: var(--accent-color);
}

.service-details .services-list a:hover {
  border-color: var(--accent-color);
}

.service-details .services-img {
  margin-bottom: 20px;
}

.service-details h3 {
  font-size: 26px;
  font-weight: 700;
}

.service-details h4 {
  font-size: 20px;
  font-weight: 700;
}

.service-details p {
  font-size: 15px;
}

.service-details ul {
  list-style: none;
  padding: 0;
  font-size: 15px;
}

.service-details ul li {
  padding: 5px 0;
  display: flex;
  align-items: center;
}

.service-details ul i {
  font-size: 20px;
  margin-right: 8px;
  color: var(--accent-color);
}

/*--------------------------------------------------------------
# Portfolio Section
--------------------------------------------------------------*/
.portfolio .portfolio-filters {
  padding: 0;
  margin: 0 auto 20px auto;
  list-style: none;
  text-align: center;
}

.portfolio .portfolio-filters li {
  cursor: pointer;
  display: inline-block;
  padding: 0;
  font-size: 18px;
  font-weight: 500;
  margin: 0 10px;
  line-height: 1;
  margin-bottom: 5px;
  transition: all 0.3s ease-in-out;
}

.portfolio .portfolio-filters li:hover,
.portfolio .portfolio-filters li.filter-active {
  color: var(--accent-color);
}

.portfolio .portfolio-filters li:first-child {
  margin-left: 0;
}

.portfolio .portfolio-filters li:last-child {
  margin-right: 0;
}

@media (max-width: 575px) {
  .portfolio .portfolio-filters li {
    font-size: 14px;
    margin: 0 5px;
  }
}

.portfolio .portfolio-content {
  position: relative;
  overflow: hidden;
}

.portfolio .portfolio-content img {
  transition: 0.3s;
}

.portfolio .portfolio-content .portfolio-info {
  opacity: 0;
  position: absolute;
  inset: 0;
  z-index: 3;
  transition: all ease-in-out 0.3s;
  background: rgba(255, 255, 255, 0.9);
  padding: 15px;
}

.portfolio .portfolio-content .portfolio-info h4 {
  font-size: 14px;
  padding: 5px 10px;
  font-weight: 400;
  color: #ffffff;
  display: inline-block;
  background-color: var(--accent-color);
}

.portfolio .portfolio-content .portfolio-info p {
  position: absolute;
  bottom: 10px;
  text-align: center;
  display: inline-block;
  left: 0;
  right: 0;
  font-size: 16px;
  font-weight: 600;
  color: rgba(51, 51, 51, 0.8);
}

.portfolio .portfolio-content .portfolio-info .preview-link,
.portfolio .portfolio-content .portfolio-info .details-link {
  position: absolute;
  left: calc(50% - 40px);
  font-size: 26px;
  top: calc(50% - 14px);
  color: #fff;
  transition: 0.3s;
  line-height: 1.2;
}

.portfolio .portfolio-content .portfolio-info .preview-link:hover,
.portfolio .portfolio-content .portfolio-info .details-link:hover {
  color: var(--accent-color);
}

.portfolio .portfolio-content .portfolio-info .details-link {
  left: 50%;
  font-size: 34px;
  line-height: 0;
}

.portfolio .portfolio-content:hover .portfolio-info {
  opacity: 1;
}

.portfolio .portfolio-content:hover img {
  transform: scale(1.1);
}

/*--------------------------------------------------------------
# Portfolio Details Section
--------------------------------------------------------------*/
.portfolio-details .portfolio-details-slider img {
  width: 100%;
}

.portfolio-details .portfolio-details-slider .swiper-pagination {
  margin-top: 20px;
  position: relative;
}

.portfolio-details .portfolio-details-slider .swiper-pagination .swiper-pagination-bullet {
  width: 12px;
  height: 12px;
  background-color: color-mix(in srgb, var(--default-color), transparent 85%);
  opacity: 1;
}

.portfolio-details .portfolio-details-slider .swiper-pagination .swiper-pagination-bullet-active {
  background-color: var(--accent-color);
}

.portfolio-details .portfolio-info {
  background-color: var(--surface-color);
  padding: 30px;
  box-shadow: 0px 0 30px rgba(0, 0, 0, 0.1);
}

.portfolio-details .portfolio-info h3 {
  font-size: 22px;
  font-weight: 700;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid color-mix(in srgb, var(--default-color), transparent 85%);
}

.portfolio-details .portfolio-info ul {
  list-style: none;
  padding: 0;
  font-size: 15px;
}

.portfolio-details .portfolio-info ul li+li {
  margin-top: 10px;
}

.portfolio-details .portfolio-description {
  padding-top: 30px;
}

.portfolio-details .portfolio-description h2 {
  font-size: 26px;
  font-weight: 700;
  margin-bottom: 20px;
}

.portfolio-details .portfolio-description p {
  padding: 0;
  color: color-mix(in srgb, var(--default-color), transparent 30%);
}

/* Enhanced Portfolio Styles */
.tech-stack {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin: 10px 0;
}

.tech-tag {
  background: color-mix(in srgb, var(--accent-color), transparent 85%);
  color: var(--accent-color);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  border: 1px solid color-mix(in srgb, var(--accent-color), transparent 70%);
}

.portfolio-info {
  position: relative;
}

.portfolio-info .demo-link {
  position: absolute;
  top: 15px;
  right: 50px;
  background: color-mix(in srgb, var(--accent-color), transparent 10%);
  color: var(--accent-color);
  width: 36px;
  height: 36px;
  border-radius: 50px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: 0.3s;
  font-size: 16px;
  z-index: 3;
}

.portfolio-info .demo-link:hover {
  background: var(--accent-color);
  color: white;
}

.portfolio-info .details-link {
  background: color-mix(in srgb, var(--default-color), transparent 10%);
}

.portfolio-info .details-link:hover {
  background: var(--default-color);
  color: var(--contrast-color);
}

.portfolio-content h4 {
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 8px;
}

.portfolio-content p {
  font-size: 14px;
  line-height: 1.4;
  margin-bottom: 10px;
  color: color-mix(in srgb, var(--default-color), transparent 30%);
}

/* Resume Download Button */
.resume-download .btn {
  padding: 12px 30px;
  font-weight: 600;
  border-radius: 25px;
  transition: all 0.3s ease;
  background-color: var(--accent-color);
  border-color: var(--accent-color);
  color: white;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.resume-download .btn:hover {
  background-color: color-mix(in srgb, var(--accent-color), black 20%);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/*--------------------------------------------------------------
# Skills Section
--------------------------------------------------------------*/
.skills-category-title {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 30px;
  color: var(--heading-color);
  display: flex;
  align-items: center;
  gap: 10px;
}

.skills-category-title i {
  color: var(--accent-color);
  font-size: 28px;
}

.skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.skill-item {
  display: flex;
  align-items: center;
  background: var(--surface-color);
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.skill-item:hover {
  transform: translateY(-5px);
}

.skill-icon {
  margin-right: 20px;
}

.skill-icon i {
  font-size: 40px;
  color: var(--accent-color);
}

.skill-info {
  flex: 1;
}

.skill-info h4 {
  margin: 0 0 10px 0;
  font-size: 18px;
  font-weight: 600;
}

.skill-info .progress {
  height: 8px;
  background: color-mix(in srgb, var(--default-color), transparent 85%);
  border-radius: 4px;
  overflow: hidden;
}

.skill-info .progress-bar {
  height: 100%;
  background: linear-gradient(45deg, var(--accent-color), color-mix(in srgb, var(--accent-color), white 20%));
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: 10px;
  font-size: 12px;
  font-weight: 600;
  color: white;
  transition: width 1s ease-in-out;
}

/* Tools Grid */
.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 40px;
}

.tool-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--surface-color);
  padding: 15px 20px;
  border-radius: 25px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  text-align: center;
}

.tool-badge:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.tool-badge i {
  font-size: 24px;
  color: var(--accent-color);
  margin-right: 10px;
}

.tool-badge span {
  font-weight: 600;
  color: var(--heading-color);
}

/* ML Skills Grid */
.ml-skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
}

.ml-skill-card {
  background: var(--surface-color);
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.ml-skill-card:hover {
  transform: translateY(-5px);
}

.ml-skill-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.ml-skill-header i {
  font-size: 32px;
  color: var(--accent-color);
  margin-right: 15px;
}

.ml-skill-header h4 {
  margin: 0;
  font-size: 20px;
  font-weight: 700;
  color: var(--heading-color);
}

.ml-skill-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.skill-tag {
  background: color-mix(in srgb, var(--accent-color), transparent 85%);
  color: var(--accent-color);
  padding: 8px 15px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  border: 1px solid color-mix(in srgb, var(--accent-color), transparent 70%);
  transition: all 0.3s ease;
}

.skill-tag:hover {
  background: var(--accent-color);
  color: white;
  transform: scale(1.05);
}

@media (max-width: 768px) {
  .skills-grid {
    grid-template-columns: 1fr;
  }

  .tools-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .ml-skills-grid {
    grid-template-columns: 1fr;
  }

  .skill-item {
    flex-direction: column;
    text-align: center;
  }

  .skill-icon {
    margin-right: 0;
    margin-bottom: 15px;
  }
}

/*--------------------------------------------------------------
# Blog Section
--------------------------------------------------------------*/
.featured-article {
  background: var(--surface-color);
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 4px 25px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.featured-article:hover {
  transform: translateY(-5px);
}

.featured-image {
  position: relative;
  height: 300px;
  overflow: hidden;
}

.featured-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.featured-article:hover .featured-image img {
  transform: scale(1.05);
}

.featured-badge {
  position: absolute;
  top: 20px;
  left: 20px;
  background: var(--accent-color);
  color: white;
  padding: 8px 15px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.featured-content {
  padding: 30px;
}

.article-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 15px;
  font-size: 14px;
  color: color-mix(in srgb, var(--default-color), transparent 30%);
}

.article-meta span {
  display: flex;
  align-items: center;
  gap: 5px;
}

.featured-content h2 {
  font-size: 28px;
  margin-bottom: 15px;
}

.featured-content h2 a {
  color: var(--heading-color);
  text-decoration: none;
  transition: color 0.3s ease;
}

.featured-content h2 a:hover {
  color: var(--accent-color);
}

.featured-content p {
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 20px;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
}

.read-more {
  color: var(--accent-color);
  font-weight: 600;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 5px;
  transition: all 0.3s ease;
}

.read-more:hover {
  color: color-mix(in srgb, var(--accent-color), black 20%);
  gap: 10px;
}

/* Blog Filters */
.blog-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: center;
  margin-bottom: 40px;
}

.filter-btn {
  background: transparent;
  border: 2px solid color-mix(in srgb, var(--default-color), transparent 80%);
  color: var(--default-color);
  padding: 10px 20px;
  border-radius: 25px;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
}

.filter-btn:hover,
.filter-btn.active {
  background: var(--accent-color);
  border-color: var(--accent-color);
  color: white;
}

/* Blog Cards */
.blog-card {
  background: var(--surface-color);
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.blog-card:hover {
  transform: translateY(-5px);
}

.blog-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.blog-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.blog-card:hover .blog-image img {
  transform: scale(1.05);
}

.blog-category {
  position: absolute;
  top: 15px;
  left: 15px;
  background: var(--accent-color);
  color: white;
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 600;
}

.blog-content {
  padding: 25px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.blog-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  font-size: 12px;
  color: color-mix(in srgb, var(--default-color), transparent 40%);
}

.blog-content h3 {
  font-size: 18px;
  margin-bottom: 15px;
  flex: 1;
}

.blog-content h3 a {
  color: var(--heading-color);
  text-decoration: none;
  transition: color 0.3s ease;
}

.blog-content h3 a:hover {
  color: var(--accent-color);
}

.blog-content p {
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 20px;
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  flex: 1;
}

/* Newsletter Signup */
.newsletter-signup {
  background: linear-gradient(135deg, var(--accent-color), color-mix(in srgb, var(--accent-color), blue 20%));
  border-radius: 20px;
  padding: 40px;
  text-align: center;
  color: white;
}

.newsletter-content h3 {
  font-size: 28px;
  margin-bottom: 15px;
  color: white;
}

.newsletter-content p {
  font-size: 16px;
  margin-bottom: 30px;
  opacity: 0.9;
}

.newsletter-form .input-group {
  max-width: 500px;
  margin: 0 auto;
}

.newsletter-form .form-control {
  border: none;
  padding: 15px 20px;
  border-radius: 25px 0 0 25px;
  font-size: 16px;
}

.newsletter-form .btn {
  padding: 15px 30px;
  border-radius: 0 25px 25px 0;
  background: white;
  color: var(--accent-color);
  border: none;
  font-weight: 600;
  transition: all 0.3s ease;
}

.newsletter-form .btn:hover {
  background: color-mix(in srgb, white, var(--accent-color) 10%);
  transform: translateY(-2px);
}

@media (max-width: 768px) {
  .featured-content {
    padding: 20px;
  }

  .featured-content h2 {
    font-size: 24px;
  }

  .article-meta {
    flex-direction: column;
    gap: 10px;
  }

  .blog-filters {
    justify-content: flex-start;
  }

  .filter-btn {
    font-size: 14px;
    padding: 8px 16px;
  }

  .newsletter-signup {
    padding: 30px 20px;
  }

  .newsletter-content h3 {
    font-size: 24px;
  }

  .newsletter-form .input-group {
    flex-direction: column;
  }

  .newsletter-form .form-control,
  .newsletter-form .btn {
    border-radius: 25px;
    margin-bottom: 10px;
  }
}

/*--------------------------------------------------------------
# Blog Post Styles
--------------------------------------------------------------*/
.post-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-top: 15px;
  font-size: 14px;
  color: color-mix(in srgb, var(--default-color), transparent 30%);
}

.post-meta span {
  display: flex;
  align-items: center;
  gap: 5px;
}

.post-content {
  background: var(--surface-color);
  padding: 40px;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.post-image img {
  border-radius: 10px;
}

.post-body h2 {
  color: var(--heading-color);
  margin-top: 30px;
  margin-bottom: 15px;
  font-size: 24px;
}

.post-body h3 {
  color: var(--heading-color);
  margin-top: 25px;
  margin-bottom: 12px;
  font-size: 20px;
}

.tech-stack-post {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin: 15px 0;
}

.tech-stack-post .tech-tag {
  background: color-mix(in srgb, var(--accent-color), transparent 85%);
  color: var(--accent-color);
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid color-mix(in srgb, var(--accent-color), transparent 70%);
}

.post-conclusion {
  background: color-mix(in srgb, var(--accent-color), transparent 90%);
  padding: 20px;
  border-radius: 10px;
  border-left: 4px solid var(--accent-color);
  margin: 30px 0;
}

.post-tags {
  border-top: 1px solid color-mix(in srgb, var(--default-color), transparent 85%);
  padding-top: 20px;
}

.post-tags .tag {
  background: color-mix(in srgb, var(--default-color), transparent 90%);
  color: var(--default-color);
  padding: 5px 12px;
  border-radius: 12px;
  font-size: 12px;
  margin-right: 8px;
  margin-bottom: 8px;
  display: inline-block;
}

.author-bio {
  background: color-mix(in srgb, var(--surface-color), var(--background-color) 50%);
  padding: 25px;
  border-radius: 10px;
  border-top: 1px solid color-mix(in srgb, var(--default-color), transparent 85%);
}

.author-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  margin-right: 20px;
  object-fit: cover;
}

.author-info h5 {
  margin-bottom: 5px;
  color: var(--heading-color);
}

.author-info p {
  margin-bottom: 8px;
  font-size: 14px;
}

/* Sidebar Styles */
.sidebar {
  padding-left: 30px;
}

.sidebar-widget {
  background: var(--surface-color);
  padding: 25px;
  border-radius: 10px;
  margin-bottom: 30px;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
}

.sidebar-widget h4 {
  color: var(--heading-color);
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: 600;
}

.recent-posts .recent-post {
  padding: 12px 0;
  border-bottom: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
}

.recent-posts .recent-post:last-child {
  border-bottom: none;
}

.recent-posts .recent-post a {
  color: var(--heading-color);
  text-decoration: none;
  font-weight: 500;
  display: block;
  margin-bottom: 5px;
}

.recent-posts .recent-post a:hover {
  color: var(--accent-color);
}

.recent-posts .post-date {
  font-size: 12px;
  color: color-mix(in srgb, var(--default-color), transparent 40%);
}

.categories {
  list-style: none;
  padding: 0;
}

.categories li {
  padding: 8px 0;
  border-bottom: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
}

.categories li:last-child {
  border-bottom: none;
}

.categories li a {
  color: var(--default-color);
  text-decoration: none;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.categories li a:hover {
  color: var(--accent-color);
}

.categories li span {
  background: var(--accent-color);
  color: white;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 11px;
}

.newsletter-widget {
  background: linear-gradient(135deg, var(--accent-color), color-mix(in srgb, var(--accent-color), blue 20%));
  color: white;
}

.newsletter-widget h4,
.newsletter-widget p {
  color: white;
}

.newsletter-widget .form-control {
  border: none;
  padding: 12px 15px;
  border-radius: 20px;
  margin-bottom: 15px;
}

.newsletter-widget .btn {
  background: white;
  color: var(--accent-color);
  border: none;
  padding: 12px 25px;
  border-radius: 20px;
  font-weight: 600;
  width: 100%;
}

@media (max-width: 768px) {
  .post-content {
    padding: 25px;
  }

  .post-meta {
    flex-direction: column;
    gap: 10px;
  }

  .sidebar {
    padding-left: 0;
    margin-top: 40px;
  }

  .author-bio .d-flex {
    flex-direction: column;
    text-align: center;
  }

  .author-avatar {
    margin-right: 0;
    margin-bottom: 15px;
  }
}

/*--------------------------------------------------------------
# Starter Section Section
--------------------------------------------------------------*/
.starter-section {
  /* Add your styles here */
}

/*--------------------------------------------------------------
# Contact Section
--------------------------------------------------------------*/
.contact .info-item {
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.1);
  padding: 20px 30px;
}

.contact .info-item .icon {
  color: var(--contrast-color);
  background-color: var(--accent-color);
  width: 56px;
  height: 56px;
  font-size: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease-in-out;
  border-radius: 50%;
  margin-right: 15px;
}

.contact .info-item h3 {
  font-size: 18px;
  font-weight: 700;
  margin: 0 0 2px 0;
}

.contact .info-item p {
  padding: 0;
  margin-bottom: 0;
  font-size: 14px;
}

.contact .info-item .social-links a {
  font-size: 24px;
  display: inline-block;
  line-height: 1;
  margin: 4px 6px 0 0;
  transition: 0.3s;
  color: color-mix(in srgb, var(--default-color), transparent 50%);
}

.contact .info-item .social-links a:hover {
  color: var(--accent-color);
}

.contact .php-email-form {
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.1);
  padding: 30px;
  margin-top: 30px;
}

@media (max-width: 575px) {
  .contact .php-email-form {
    padding: 20px;
  }
}

.contact .php-email-form input[type=text],
.contact .php-email-form input[type=email],
.contact .php-email-form textarea {
  color: var(--default-color);
  background-color: color-mix(in srgb, var(--background-color), transparent 50%);
  border-color: color-mix(in srgb, var(--default-color), transparent 80%);
  font-size: 14px;
  padding: 10px 15px;
  box-shadow: none;
  border-radius: 0;
}

.contact .php-email-form input[type=text]:focus,
.contact .php-email-form input[type=email]:focus,
.contact .php-email-form textarea:focus {
  border-color: var(--accent-color);
}

.contact .php-email-form input[type=text]::placeholder,
.contact .php-email-form input[type=email]::placeholder,
.contact .php-email-form textarea::placeholder {
  color: color-mix(in srgb, var(--default-color), transparent 70%);
}

.contact .php-email-form button[type=submit] {
  color: var(--contrast-color);
  background: var(--accent-color);
  border: 0;
  padding: 10px 30px;
  transition: 0.4s;
  border-radius: 50px;
}

.contact .php-email-form button[type=submit]:hover {
  background: color-mix(in srgb, var(--accent-color), transparent 20%);
}